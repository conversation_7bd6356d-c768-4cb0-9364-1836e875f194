<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="6">
            <item index="0" class="java.lang.String" itemvalue="xlwings" />
            <item index="1" class="java.lang.String" itemvalue="selenium" />
            <item index="2" class="java.lang.String" itemvalue="webdriver-manager" />
            <item index="3" class="java.lang.String" itemvalue="Pillow" />
            <item index="4" class="java.lang.String" itemvalue="keyboard" />
            <item index="5" class="java.lang.String" itemvalue="pyautogui" />
          </list>
        </value>
      </option>
    </inspection_tool>
  </profile>
</component>